<!doctype html>
<html class="no-js" xmlns="http://www.w3.org/1999/html" data-ng-app="SportWrench">
<head>
    <meta charset="utf-8"/>
    <title>SportWrench - The Home of Sporting Events</title>
    <meta name="description" content="SportWrench - The Home of Sporting Events"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1">

    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/jquery/2.2.4/jquery.min.js"
        integrity="sha512-DUC8yqWf7ez3JD1jszxCWSVB0DMP78eOyBpMa5aJki1bIRARykviOuImIczkxlj1KhVSyS16w2FSQetkD4UU2w=="
        crossorigin="anonymous"
        referrerpolicy="no-referrer"
    ></script>

    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"
        integrity="sha512-uto9mlQzrs59VwILcLiRYeLKPPbS/bT71da/OEBYEwcdNUk8jYIy+D176RYoop1Da+f9mvkYrmj5MCLZWEtQuA=="
        crossorigin="anonymous"
        referrerpolicy="no-referrer"
    ></script>

    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.8.3/underscore-min.js"
        integrity="sha512-7fNh7OUGa7bdpmSQ81iNxgBywspNTxVxBxfbT1gSnQ124VGfksj3AR/QGhdYaO8ZLHBLSoaa+VsVDgw795eBaw=="
        crossorigin="anonymous"
        referrerpolicy="no-referrer"
    ></script>

    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"
        integrity="sha512-i2CVnAiguN6SnJ3d2ChOOddMWQyvgQTzm0qSgiKhOqBMGCx4fGU5BtzXEybnKatWPDkXPFyCI0lbG42BnVjr/Q=="
        crossorigin="anonymous"
        referrerpolicy="no-referrer"
    ></script>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/angular.js/1.5.6/angular-csp.css"/>
    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/angular.js/1.5.6/angular.min.js"
        integrity="sha512-rTAljISl+fitBnL2/6HhbiENEHd87W0DnTh1J7CcRD/dTLTycfceHs1uQEsR83nD86ON4k9N8H8S5OwGzoA9jQ=="
        crossorigin="anonymous"
        referrerpolicy="no-referrer"
    ></script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-16KVVKWFYJ"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'G-16KVVKWFYJ', {'send_page_view': false});
    </script>

    <!-- Webpack CSS bundles -->
    <% for (const css of htmlWebpackPlugin.files.css) { %>
    <link rel="stylesheet" href="<%= css %>"/>
    <% } %>
</head>
<body data-ng-controller="MainController" ng-cloak ng-class="{'inin':true}">
    <div ng-class="{'navbar': true, 'navbar-default': true, 'navbar--m0':true, 'navbar-fixed-top':false}">
        <div class="container">
            <div class="navbar-header">
                <button type="button" class="mobile-menu navbar-toggle" data-toggle="collapse" data-target=".navbar-ex1-collapse" ng-click="isCollapsed = !isCollapsed">
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <div class="esw-state">
                    <a ng-click="goBack()" class="btn btn-default"><i class="fa fa-chevron-left"></i></a>
                    <ui-breadcrumbs class="sw_header_breadcrumbs"
                        displayname-property="data.displayName"
                        template-url="components/breadcrumbs/uiBreadcrumbs.html"
                        abstract-proxy-property="data.breadcrumbProxy">
                    </ui-breadcrumbs>
                    <a ng-click="openFavorites()" class="btn btn-default" style="float:left; margin-right:5px;"><i ng-class="{'red-star': !favorites.length}" class="fa fa-star"></i></a>
                    <div class="tap-star" ng-if="!favorites.length">Tap <i class="fa fa-star red-star"></i> to save page.</div>
                </div>
            </div>
            <nav collapse="isCollapsed" class="collapse navbar-collapse navbar-ex1-collapse">
                <ul class="nav navbar-nav navbar-right" ng-controller="NavButtonsCtrl">
                    <li>
                        <a ng-click="toState('events.event.schedule')" ng-show="$root.isPrivateLink()" ui-sref-active="active"><i class="fa fa-calendar"></i> Court Grid</a>
                    </li>
                    <li>
                        <a ng-click="toState('events.event.clubs')" ng-show="$root.isPrivateLink() && $root.hasClubs()" ui-sref-active="active"><i class="fa fa-users"></i> Clubs</a>
                    </li>
                    <li>
                        <a ng-click="toState('events.event.divisions')" ng-show="$root.isPrivateLink()" ui-sref-active="active"><i class="fa fa-th"></i> Divisions</a>
                    </li>
                    <li>
                        <a ui-sref="events" ui-sref-active="active"><i class="fa fa-search"></i> Events</a>
                    </li>
                    <li>
                        <a href="{{HOME_PAGE_URL}}/#/"><i class="fa fa-home"></i> SW Home</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <div
        class="block-height-min" ng-class="{'container': isNotFullPage(), 'container bracket-container': isPoolBracketPage()}"
        ui-view="rootView"
        autoscroll="true"
        >
    </div>

    <confirmation visible="{{$root.confirm.getVisibility()}}" message="{{$root.confirm.getMessage()}}" agree="$root.confirm.confirm()" cancel="$root.confirm.cancel()" class="notification-holder"></confirmation>

    <div ng-controller="notificationCtrl" class="notification-holder">
        <alert ng-repeat="notification in notifications" type="{{notification.type}}" close="closeNotification($index)">{{notification.message}}</alert>
    </div>

    <footer class="container-fluid">
        <div class="container">
            <div class="copyright pull-left">&copy; SportWrench Inc. {{year}}. All rights reserved</div>
        </div>
    </footer>

    <!-- Webpack JS bundles -->
    <% for (const js of htmlWebpackPlugin.files.js) { %>
    <script src="<%= js %>"></script>
    <% } %>

    <!-- BEGIN SENTRY -->
    <!-- Initialize fallback Sentry object immediately to prevent errors -->
    <script>
        window.Sentry = window.Sentry || {
            init: function() { return this; },
            captureException: function() {},
            captureMessage: function() {},
            captureEvent: function() {},
            setUser: function() {},
            setTag: function() {},
            setTags: function() {},
            setContext: function() {},
            setExtra: function() {},
            setExtras: function() {},
            withScope: function(callback) {
                if (typeof callback === 'function') {
                    callback({ setTag: function() {}, setExtra: function() {} });
                }
            },
            configureScope: function(callback) {
                if (typeof callback === 'function') {
                    callback({ setTag: function() {}, setExtra: function() {} });
                }
            },
            lastEventId: function() { return null; },
            Integrations: {
                Angular: function() { return {}; },
                BrowserTracing: function() { return {}; },
                Replay: function() { return {}; }
            }
        };
    </script>

    <!-- Load Sentry asynchronously -->
    <script>
        function loadSentryScripts() {
            // Load main Sentry bundle
            var sentryScript = document.createElement('script');
            sentryScript.src = 'https://browser.sentry-cdn.com/7.109.0/bundle.tracing.replay.min.js';
            sentryScript.integrity = 'sha384-yUeLF1YOBa+gnOMSLMiMI/kkW40NC9CjlKj9I8sVm98HyWD6s57bVosZwg+Hp8Cb';
            sentryScript.crossOrigin = 'anonymous';
            sentryScript.async = true;

            sentryScript.onload = function() {
                // After main bundle loads, load Angular integration
                var angularScript = document.createElement('script');
                angularScript.src = 'https://browser.sentry-cdn.com/6.19.7/angular.min.js';
                angularScript.crossOrigin = 'anonymous';
                angularScript.async = true;

                angularScript.onload = function() {
                    // Initialize Sentry after both scripts are loaded
                    initializeSentry();
                };

                angularScript.onerror = function() {
                    console.warn('Failed to load Sentry Angular integration. Using fallback.');
                };

                document.head.appendChild(angularScript);
            };

            sentryScript.onerror = function() {
                console.warn('Failed to load Sentry. Using fallback.');
            };

            document.head.appendChild(sentryScript);
        }

        function initializeSentry() {
            try {
                Sentry.init({
                    dsn: "https://<EMAIL>/4",
                    beforeSend: function(event) {
                        // filter out UnhandledRejection errors that have no information
                        if (event && event.exception && event.exception.values && event.exception.values.length) {
                            const {type, value} = event.exception.values[0];

                            const nonErrorPromiseRejectionType = 'UnhandledRejection';
                            const nonErrorPromiseRejectionValue = 'Non-Error promise rejection captured';

                            if (type === nonErrorPromiseRejectionType && value.includes(nonErrorPromiseRejectionValue)) {
                                return null;
                            }
                        }
                        return event;
                    },
                    integrations: [
                        new Sentry.Integrations.Angular(),
                        new Sentry.BrowserTracing(),
                        new Sentry.Replay({
                            maskAllText: false,
                            blockAllMedia: false,
                        })
                    ],
                    ignoreErrors: [
                        /captcha/i, /timeout/i, /localStorage/i, /unauthorized/i,
                        /modulerr/i, /validation/i, /__gCrWeb/i,
                        /Non-Error promise rejection captured/i,
                        /Object captured as exception/i,
                        /Object captured as promise/i,
                    ],
                    ignoreUrls: [
                        /^chrome(-extension)?:\/\//i,
                    ],
                    tracesSampleRate: 1.0,
                    replaysSessionSampleRate: 0,
                    replaysOnErrorSampleRate: 0.5
                });
            } catch (e) {
                console.warn('Error initializing Sentry:', e);
            }
        }

        // Load Sentry scripts asynchronously
        loadSentryScripts();
    </script>
    <!-- END SENTRY -->
</body>
</html>
