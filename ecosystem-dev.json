{"apps": [{"name": "app-dev", "script": "app.js", "instances": 1, "exec_mode": "cluster", "log_date_format": "YYYY-MM-DD HH:mm:ss Z", "color": false, "restart_delay": 5000, "args": "--dev", "kill_timeout": 10000, "wait_ready": true, "listen_timeout": 20000}, {"name": "scheduler-worker-dev", "script": "scheduler/scheduler.worker.js", "args": "--dev", "exec_mode": "fork", "log_date_format": "YYYY-MM-DD HH:mm:ss Z", "color": false, "max_restarts": 10, "restart_delay": 1000, "kill_timeout": 10000, "env": {"NODE_ENV": "development"}}, {"name": "workers-queue-dev", "script": "worker/main.js", "args": "--dev", "exec_mode": "fork", "log_date_format": "YYYY-MM-DD HH:mm:ss Z", "color": false, "max_restarts": 10, "restart_delay": 1000, "kill_timeout": 10000, "env": {"NODE_ENV": "development"}}]}