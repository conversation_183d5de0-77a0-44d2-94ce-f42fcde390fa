/**
 *
 * All routes placed in userconfig/routes
 *
 */


const includeAll = require('include-all');
const path = require('path');

const routesFolder = path.resolve(__dirname, '..', 'userconfig', 'routes');

function getRoutes() {
    return includeAll({
        dirname         : (routesFolder),
        filter          : /^([^.]+)\.(?:(?!md|txt).)+$/,
        depth           : 1,
        caseSensitive   : true
    });
}

let routes = getRoutes();

let requiredRoutes = _.reduce(
    Object.keys(routes), (result, route) =>
        Object.assign(result, require(path.resolve(routesFolder, route)))
    , {}
);

module.exports.routes = requiredRoutes;
