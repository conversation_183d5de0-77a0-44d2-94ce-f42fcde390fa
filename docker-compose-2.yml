version: '3.8'
services:
    web:
        image: sw-main-2
        container_name: sw-main-2
        user: "${UID}:${GID}"
        logging:
            driver: loki
            options:
                loki-url: "http://gf.uastage.com:3100/loki/api/v1/push" 
        build:
            context: .
            dockerfile: Dockerfile
            args:
                UID: ${UID}
                GID: ${GID}
                ENV: ${NODE_ENV}
                LOG_PG_CS: ${LOG_PG_CS}
                LOG_APP_ID: ${LOG_APP_ID}
        ports:
            - "4000:3000"
        environment:
            - SW_DB=${SW_DB}
            - REDIS_URL=${REDIS_URL}
            - WORK_DIR=${WORK_DIR}
            - NODE_ENV=${NODE_ENV}
            - LOG_PG_CS=${LOG_PG_CS}
            - LOG_APP_ID=${LOG_APP_ID}
        # https://stackoverflow.com/questions/58026998/docker-does-not-update-volume-inside-container
        # https://github.com/docker/for-win/issues/5530
        volumes:
            # - ${WORK_DIR}:/home/<USER>/app:consistent
            - type: bind
              source: ${WORK_DIR}
              target: /home/<USER>/app
            - ${WORK_DIR}/../.pm2/logs:/home/<USER>/.pm2/logs
            - ${WORK_DIR}/../logs-2:/home/<USER>/logs
            - ${WORK_DIR}/../connection-2:/home/<USER>/connection
            - ${WORK_DIR}/../sw-config-2:/home/<USER>/sw-config
            - ${WORK_DIR}/../passbook_keys:/home/<USER>/passbook_keys
        stop_grace_period: 1m
        restart: always
        init: true
        deploy:
            resources:
                limits:
                    memory: 1500M
networks:
    default:
        name: sw
        external: true
