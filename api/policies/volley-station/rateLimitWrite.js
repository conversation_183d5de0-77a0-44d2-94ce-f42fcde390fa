function keySelector(req) {
    return req.user && req.user.clientId;
}

module.exports = function(req, res, next) {
    if (!module.exports.rateLimiter) {
        module.exports.rateLimiter = RateLimiterService.createRateLimiter(
            'volley-station:write',
            sails.config.volleyStation.rateLimit.write,
            keySelector
        );
    }

    return module.exports.rateLimiter(req, res, next);
};
