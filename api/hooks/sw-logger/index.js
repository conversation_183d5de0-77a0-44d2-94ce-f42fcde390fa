const argv = require('optimist').argv;
const winston = require('winston');
const util = require('util');
const path = require('path');
const WinstonGraylog2 = require('winston-graylog2');
const WinstonSyslog = require('../../lib/log/WinstonSyslog');
const SWLoggerService = require('../../lib/log/SWLogger');
const SWLogger = new SWLoggerService(winston);
const { combine, label, timestamp, printf, colorize } = winston.format;

module.exports = (sails) => ({
    configure() {
        const logDir = process.env.SW_LOGGER_FILE_PATH || '../logs';
        const prefix = argv.prod ? 'prod_' : 'dev_';

        function logfile(name) {
            return path.join(logDir, prefix + name);
        }

        function logLevel() {
            return argv.prod ? 'warn' : 'silly';
        }

        const initialTransports = [
            new winston.transports.Console(),
        ];

        const exceptionHandlers = [
            new winston.transports.File({ filename: logfile('exception.log') }),
        ];
        const rejectionHandlers = [
            new winston.transports.File({ filename: logfile('rejections.log') }),
        ];

        const purchase_log = SWLogger.add('purchase_log', {
            level: logLevel(),
            exitOnError: false,
            format: getCustomFormat('PURCHASE'),
            transports: [
                ...initialTransports,
                new winston.transports.File({ filename: logfile('purchase.log') })
            ],
            exceptionHandlers,
            rejectionHandlers,
        });

        const errorsTransports = _.clone(initialTransports, true);
        if(sails.config.log.syslogOptions) {
            const transport = new WinstonSyslog(sails.config.log.syslogOptions);
            errorsTransports.push(transport);
        }
        if(sails.config.log.graylogOptions) {
            const transport = new WinstonGraylog2(sails.config.log.graylogOptions);
            errorsTransports.push(transport);
        }

        const errors_log = SWLogger.add('errors_log', {
            level: 'error',
            exitOnError: false,
            format: getCustomFormat('ERROR'),
            transports: [
                ...errorsTransports,
                new winston.transports.File({ filename: logfile('errors.log') })
            ],
            exceptionHandlers,
            rejectionHandlers,
        });
        const debug_log = SWLogger.add('debug_log', {
            level: logLevel(),
            exitOnError: false,
            format: getCustomFormat('DEBUG'),
            transports: [
                ...initialTransports,
                new winston.transports.File({ filename: logfile('application.log') })
            ],
            exceptionHandlers,
            rejectionHandlers,
        });
        const event_notifications = SWLogger.add('event_notifications', {
            level: logLevel(),
            exitOnError: false,
            format: getCustomFormat('EVENT NOTIFICATIONS'),
            transports: [
                ...initialTransports,
                new winston.transports.File({ filename: logfile('event_notifications.log') })
            ],
            exceptionHandlers,
            rejectionHandlers,
        });
        const sent_notifications = SWLogger.add('sent_notifications', {
            level: logLevel(),
            exitOnError: false,
            format: getCustomFormat('NOTIFICATION SENT'),
            transports: [
                ...initialTransports,
                new winston.transports.File({ filename: logfile('sent_notifications.log') })
            ],
            exceptionHandlers,
            rejectionHandlers,
        });
        const webpoint_parse = SWLogger.add('webpoint_parse', {
            level: logLevel(),
            exitOnError: false,
            format: getCustomFormat('WEBPOINT PARSE'),
            transports: [
                ...initialTransports,
                new winston.transports.File({ filename: logfile('webpoint_parse.log') })
            ],
            exceptionHandlers,
            rejectionHandlers,
        });
        const sw_errors = SWLogger.add('sw_frontend_errors', {
            level: 'error',
            exitOnError: false,
            format: getCustomFormat('SW CLIENT'),
            transports: [
                ...initialTransports,
                new winston.transports.File({ filename: logfile('sw_frontend_errors.log') })
            ],
            exceptionHandlers,
            rejectionHandlers,
        });
        const esw_errors = SWLogger.add('esw_frontend_errors', {
            level: 'error',
            exitOnError: false,
            format: getCustomFormat('ESW CLIENT'),
            transports: [
                ...initialTransports,
                new winston.transports.File({ filename: logfile('esw_frontend_errors.log') })
            ],
            exceptionHandlers,
            rejectionHandlers,
        });

        global.loggers = {
            errors_log,
            debug_log,
            purchase_log,
            event_notifications,
            sent_notifications,
            wp: webpoint_parse,
            sw_client: sw_errors,
            esw_client: esw_errors,
        };

        global.logger = debug_log;

        process.on('unhandledRejection', (err, p) => {
            errors_log.error('Caught an unhandled rejection', JSON.stringify(err), p);
        });
        process.on('uncaughtException', (err) => {
            errors_log.error('Caught an unhandled exception', err);
        });
    }
});

function getCustomFormat (labelString) {
    return combine(
        colorize({ all: true }),
        timestamp({ format: 'YYYY-MM-DD hh:mm:ss.SSS A' }),
        label({ label: labelString }),
        printf((info) => {
            const SPLAT = Symbol.for('splat');
            const splatArgs = info[SPLAT] || [];
            const message =  util.format(info.message, ...splatArgs);

            return `${info.timestamp} - ${info.level}: [${info.label}]: ${message}`
        })
    )
}
