'use strict';

const 
    co              = require('co'),
    moment          = require('moment'),
    knex = require('knex')({client:'pg'}),

    swUtils                     = require('../lib/swUtils'), 
    SchInfoSchema               = require('../validation-schemas/official').sch_info,
    OfficialsResultsService     = require('./official/_ResultsService'),
    CheckinService              = require('./official/_EventCheckin'),
    ProfileService              = require('./official/_ProfileService'),
    EventManageService          = require('./official/_EventManageService'),
    AssignmentsService          = require('./official/_AssignmentsService'),
    ClothingService             = require('./official/_ClothingService'),
    SanctioningCheckUpdateService = require('./official/_SanctioningCheckUpdateService'),
    EventOfficialHistoryService = require('./official/_EventOfficialHistoryService'),
    EventOfficialRateService    = require('./official/_EventOfficialRateService'),
    EventOfficialPayoutService  = require('./official/_EventOfficialPayoutService'),
    EventOfficialAdditionalPaymentService = require('./official/_EventOfficialAdditionalPaymentService'),
    EntryQRCodeService = require('./official/_EntryQRCodeService'),
    OfficialEventsList = require('./official/_OfficialEventsListService');

const SCHEDULE_APP_LINK = `${sails.config.urls.home_page.baseUrl}/officials-assignment/#/event/`;

const ROLE_ALIASES = {
    official: {
        DELETED: 'deleted',
        ROLE   : 'is_official',
        STATUS : 'work_status'
    },
    staff: {
        DELETED: 'staff_deleted',
        ROLE   : 'is_staff',
        STATUS : 'staff_work_status'
    }
};

const NOT_UPDATED_PROFILES_STATUS = 'not_updated_profiles';
const WITHDRAWN_STATUS = 'Withdrawn';

const ALLOWED_WORK_STATUSES = [WITHDRAWN_STATUS, NOT_UPDATED_PROFILES_STATUS];

let OfficialsService = {
    SPECIAL_SELECTION_TYPE: {
        CLOTHING: 'clothing',
        TRAVEL  : 'travel',
        WEBPOINT: 'webpoint',
        ENTRY_QR_CODE: 'entry_qr_code'
    },
    
    _getMembersListFilters: function (filters, specialSelection) {
        let { role, search, hotel, work_status, daysSelection, notAvailableDays, selectedIDs} = filters;

        let expression = squel.expr();
        let roleExpr   = squel.expr().and(`eof.${ROLE_ALIASES[role].ROLE} IS TRUE`);

        const isAllowedWorkStatus = work_status && ALLOWED_WORK_STATUSES.some(status => status === work_status);

        // Filter officials with special selection only if work status is not 'Withdrawn'
        if(!specialSelection || (work_status && isAllowedWorkStatus)) {
            roleExpr.or(`eof.${ROLE_ALIASES[role].ROLE} IS FALSE AND eof.${ROLE_ALIASES[role].DELETED} IS NOT NULL`);
        }

        if(work_status) {
            if(work_status === WITHDRAWN_STATUS) {
                expression.and(`eof.${ROLE_ALIASES[role].DELETED} IS NOT NULL`);
            } else if (work_status === NOT_UPDATED_PROFILES_STATUS) {
                expression.and('of.profile_completed_at IS NULL');
            } else {
                expression.and(`INITCAP(eof.${ROLE_ALIASES[role].STATUS}::TEXT) = INITCAP(?)`, work_status);
            }
        }

        expression.and(roleExpr);

        if(search) {
            let formattedSearch = '%' + search + '%';

            let searchExpr = squel.expr()
                .or('u.email ILIKE ?'      , formattedSearch)
                .or('of.usav_num ILIKE ?'  , formattedSearch)
                .or('u.first ILIKE ?'      , formattedSearch)
                .or('u.last ILIKE ?'       , formattedSearch);

            expression.and(searchExpr);
        }

        if(hotel) {
            if (hotel === "yes") {
                expression.and('eof.need_hotel_room IS TRUE');
            } else if(hotel === "no") {
                expression.and('eof.need_hotel_room IS NOT TRUE');
            } else if(hotel === "notSelected") {
                expression.and('eof.need_hotel_room IS TRUE');
                expression.and(`eof.hotel_nights_required::TEXT = '{}'::TEXT OR eof.hotel_nights_required IS NULL`);
            }
        }

        if(daysSelection) {
            Object.keys(daysSelection).forEach(day => {
                if(daysSelection[day]) {
                    expression.and(`CAST(eof.schedule_availability->>? AS INTEGER) = 1`, day);
                }
            })
        }

        if(notAvailableDays) {
            Object.keys(notAvailableDays).forEach(day => {
                if(notAvailableDays[day]) {
                    expression.and(
                        `(CAST(eof.schedule_availability->>? AS INTEGER) <> 1 OR eof.schedule_availability->>? IS NULL)`
                        , day, day
                    );
                }
            })
        }

        if(Array.isArray(selectedIDs) && selectedIDs.length) {
            expression.and('of.official_id IN ?', selectedIDs);
        }

        return expression;
    },
    getOfficialsFieldsSelection: function (specialSelection) {
        let fullSelection = [
            'of.is_official', 'eof.is_staff', 'eof.use_clinic', 'of.address', 'of.city', 'of.state', 'of.zip', 'eof.staff_payment_option',
            'eof.additional_restrictions', 'of.advancement', `COALESCE(eof.rank, of.rank) "rank"`,
            'of.usav_num', 'of.aau_number', 'eof.work_status', 'eof.staff_work_status',
            'u.first', 'u.last', 'u.email', 'of.official_id', 'eof.head_official', 'eof.hotel_nights_required',
            'eof.need_hotel_room', 'eof.security_pin', 'eof.event_official_id', 'eof.deleted', 'eof.staff_deleted',
            'eof.is_email_notifications_receiver', ' eof.is_email_contact_provider', 'u.phone_mob',
            `TO_CHAR(eof.staff_arrival_datetime, 'YYYY-MM-DD HH24:MI:SS') "staff_arrival_datetime"`,
            'eof.schedule_availability',
            'eof.event_id',
            `(
                SELECT NULLIF(NULLIF(COUNT(*), 1), 0) FROM event_official eofc
                WHERE UPPER(eof.schedule_name) = UPPER(eofc.schedule_name)
                      AND eofc.event_id = eof.event_id
                      AND eofc.deleted ISNULL
                      AND eofc.work_status IN ('pending', 'approved', 'waitlisted')
            ) "schedule_name_duplicates"`,
            `(
                CASE  
                    WHEN NULLIF(additional_restrictions, '') IS NULL  
                    THEN FALSE 
                ELSE TRUE  
                END
            ) "has_restrictions"`,
            `of.profile_completed_at IS NOT NULL AS profile_completed`,
            `u.country`
        ];

        let webpointSelection = [
            `TO_CHAR(of."webpoint_modified", 'Mon DD, YYYY HH12:MI AM')                  "webpoint_modified"`,
            `TO_CHAR(of."bg_expire_date", 'Mon DD, YYYY')                                "bg_expire_date"`,
            `TO_CHAR(of."aau_bg_expire_date", 'Mon DD, YYYY')                            "aau_bg_expire_date"`,
            `TO_CHAR(of."mbr_expire_date", 'Mon DD, YYYY')                               "mbr_expire_date"`,
            `TO_CHAR(of."aau_expire_date", 'Mon DD, YYYY')                               "aau_expire_date"`,
            `TO_CHAR(NULLIF(of."safesport_end_date", '')::TIMESTAMP, 'Mon DD, YYYY')     "safesport_end_date"`,
            `TO_CHAR(of."aau_safesport_end_date", 'Mon DD, YYYY')                        "aau_safesport_end_date"`,
            `(
                CASE 
                    WHEN of.mbr_expire_date > e."date_end" OR CURRENT_DATE > e."date_end"
                    THEN ''

                    WHEN of.mbr_expire_date < CURRENT_DATE
                    THEN 'expired'
                    
                    WHEN of.mbr_expire_date >= CURRENT_DATE
                    THEN 'expires'
                    
                    ELSE ''
                END    
            ) "mbr_expiration"`,
            `(
                CASE 
                    WHEN of.aau_expire_date > e."date_end" OR CURRENT_DATE > e."date_end"
                    THEN ''

                    WHEN of.aau_expire_date < CURRENT_DATE
                    THEN 'expired'
                    
                    WHEN of.aau_expire_date >= CURRENT_DATE
                    THEN 'expires'
                    
                    ELSE ''
                END    
            ) "aau_expiration"`,
            `(
                CASE 
                    WHEN of.bg_expire_date > e."date_end"
                    THEN ''

                    WHEN of.bg_expire_date < CURRENT_DATE
                    THEN 'expired'
                    
                    WHEN of.bg_expire_date >= CURRENT_DATE
                    THEN 'expires'

                    ELSE ''
                END    
            ) "bg_expiration"`,
            `(
                CASE 
                    WHEN of.aau_bg_expire_date > e."date_end"
                    THEN ''

                    WHEN of.aau_bg_expire_date < CURRENT_DATE
                    THEN 'expired'
                    
                    WHEN of.aau_bg_expire_date >= CURRENT_DATE
                    THEN 'expires'

                    ELSE ''
                END    
            ) "aau_bg_expiration"`,
            `(
                CASE 
                    WHEN NULLIF(of."safesport_end_date", '')::DATE > e."date_end"
                    THEN ''

                    WHEN NULLIF(of."safesport_end_date", '')::DATE < CURRENT_DATE 
                    THEN 'expired'        

                    WHEN NULLIF(of."safesport_end_date", '')::DATE >= CURRENT_DATE 
                    THEN 'expires'

                    ELSE ''
                END    
            ) "safesport_expiration"`,
            `(
                CASE 
                    WHEN of."aau_safesport_end_date" > e."date_end"
                    THEN ''

                    WHEN of."aau_safesport_end_date" < CURRENT_DATE 
                    THEN 'expired'        

                    WHEN of."aau_safesport_end_date" >= CURRENT_DATE 
                    THEN 'expires'

                    ELSE ''
                END    
            ) "aau_safesport_expiration"`,
            "eof.safesport_manual_ok",
            "eof.bg_manual_ok",
            "eof.aau_safesport_manual_ok",
            "eof.aau_bg_manual_ok",
            `(
                CASE 
                    WHEN of.background_screening = '2' 
                        THEN 'YES' 
                        ELSE 'NO' 
                END
            ) as "background_screening"`,
            `(
                CASE 
                    WHEN of.aau_bg_screening = '2' 
                        THEN 'YES' 
                        ELSE 'NO' 
                END
            ) as "aau_bg_screening"`,
            `(
                CASE 
                    WHEN of.safesport_statusid = '2'
                        THEN 'YES' 
                        ELSE 'NO' 
                END
            ) as "safesport_status"`,
            `(
                CASE 
                    WHEN of.aau_safesport_statusid = '2'
                        THEN 'YES' 
                        ELSE 'NO' 
                END
            ) as "aau_safesport_status"`
        ];

        let clothingSelection = [
            'of.official_id', 'u.first', 'u.last', 'u.gender',
            'of.special_sizing_requests'
        ];

        let travelSelection = [
            'u.first', 'u.last', 'eof.travel_method', 'eof.hotel_nights_required', 'eof.roommate_preference',
            'TO_CHAR(eof.staff_arrival_datetime, \'YYYY-MM-DD HH24:MI:SS\') "arrival_time"',
            'eof.need_hotel_room', 'TO_CHAR(eof.departure_datetime, \'YYYY-MM-DD HH24:MI:SS\') "departure_datetime"',
            'eof.additional_restrictions'
        ];

        let entryQRCodeSelection = ['of.official_id', 'of.checkin_barcode', 'u.first', 'u.last', 'u.email'];

        if(specialSelection === this.SPECIAL_SELECTION_TYPE.CLOTHING) {
            return clothingSelection;
        } else if(specialSelection === this.SPECIAL_SELECTION_TYPE.TRAVEL) {
            return travelSelection;
        } else if (specialSelection === this.SPECIAL_SELECTION_TYPE.WEBPOINT) {
            return webpointSelection;
        } else if (specialSelection === this.SPECIAL_SELECTION_TYPE.ENTRY_QR_CODE) {
            return entryQRCodeSelection;
        } else {
            return fullSelection.concat(webpointSelection);
        }
    },

    _getOfficialsListQuery: function (eventID, params = {}, specialSelection, needParticipantConfirmedData) {
        let fields = this.getOfficialsFieldsSelection(specialSelection);

        let whereExpr = this._getMembersListFilters(params, specialSelection);
        let query     =
            squel.select().from('event_official', 'eof')
                .fields(fields)
                .field('oar.name', 'official_additional_role')
                .left_join('official', 'of', 'of.official_id = eof.official_id')
                .left_join('user', 'u', 'u.user_id = of.user_id AND u.role_staff IS TRUE')
                .left_join('event_official_additional_role', 'eoar', 'eoar.event_official_id = eof.event_official_id')
                .left_join('official_additional_role', 'oar', 'oar.official_additional_role_id = eoar.official_additional_role_id')
                .join('event', 'e', 'e.event_id = eof.event_id')
                .where('eof.event_id = ?', eventID)
                .order('eof.head_official, u.last')
                .where(whereExpr);

        if(!specialSelection) {
            query.field(`
                    ( 
                        CASE 
                            WHEN NULLIF(eof.${ROLE_ALIASES[params.role].DELETED}, NULL) IS NULL 
                            THEN (INITCAP(eof.${ROLE_ALIASES[params.role].STATUS}::TEXT))
                            ELSE 'Withdrawn'
                        END
                    )
                `, 'work_status_cap')
        }

        if (needParticipantConfirmedData) {
            query.field(`
                TO_CHAR(
                    eof.is_official_participation_confirmed::TIMESTAMPTZ AT TIME ZONE e.timezone, 'MM/DD, HH12:MI AM'
                ) AS is_official_participation_confirmed
            `);
        }

        return query;
    },
    // partially covered 😄👍
    _getOfficialsList: function (eventID, params = {}, specialSelection, needParticipantConfirmedData) {
        const query = this._getOfficialsListQuery(eventID, params, specialSelection, needParticipantConfirmedData);
        return Db.query(query).then(result => result.rows);
    },
    getClothingList: function (eventID, filters) {
        return this._getOfficialsList(eventID, filters, this.SPECIAL_SELECTION_TYPE.CLOTHING).then(members => {
            return { members };
        })
    },
    getClothingListQuery: function (eventID, filters) {
        return this._getOfficialsListQuery(eventID, filters, this.SPECIAL_SELECTION_TYPE.CLOTHING);
    },

    getQREntryCodeListQuery: function (eventID, filters) {
        return this._getOfficialsListQuery(eventID, filters, this.SPECIAL_SELECTION_TYPE.ENTRY_QR_CODE);
    },

    getTravelList: function (eventID, filters) {
        const specialSelection = this.SPECIAL_SELECTION_TYPE.TRAVEL;

        return this._getOfficialsList(eventID, filters, specialSelection)
            .then(this.__formatHotelNightsToDayOfWeek);
    },
    getTravelListQuery: function (eventID, filters) {
        const specialSelection = this.SPECIAL_SELECTION_TYPE.TRAVEL;

        return this._getOfficialsListQuery(eventID, filters, specialSelection);
    },
    __formatHotelNightsToDayOfWeek: function (members) {
        if(!members || !members.length) {
           return [];
        }

        return members.map(member => {
            if(member.hotel_nights_required && member.need_hotel_room) {
                let nights = member.hotel_nights_required.dates;
                let result = [];
                Object.keys(nights).forEach(night => {
                    if(nights[night]) {
                        let dayOfWeek = moment(night, 'MMDDYYYY').format('dd');

                        result.push(dayOfWeek);
                    }
                });

                member.hotel_nights_required.dates = result.join(', ');
            }

            return member;
        });
    },

    // covered 😄👍
    _getEventSettings: function (eventID) {
        let query = 
            `SELECT
                (
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("d"))), '[]'::JSON)
                    FROM (
                        SELECT
                            ('day_' || ROW_NUMBER() OVER()) "day_num",
                            TO_CHAR("day", 'Dy, Mon DD') "date"
                        FROM GENERATE_SERIES(e.date_start, e.date_end, '1 day'::INTERVAL) "day"
                    ) "d"
                ) "days",
                e.use_clinic,
                e.official_additional_role_enable,
                e.sport_sanctioning_id
            FROM "event" e
            WHERE e.event_id = $1`;

        return Db.query(query, [eventID]).then(result => result.rows[0] || null);
    },

    /**
     * Returns list of Event Staff
     *
     * This method is partially covered
     * 
     * @param  {Number} eventID              - Event Identifier
     * @param  {Object} params               - Additional params
     * @param  {String} params.role          - Filter list by role (staff/official);
     * @return {Object}
     */
    getAll: function (eventID, params) {
        if (!Number.isInteger(eventID)) {
            return Promise.reject({ validation: 'Event Identifier should be an integer' })
        }

        return Promise.all([
            this._getOfficialsList(eventID, params, null, true),
            this._getEventSettings(eventID)
        ]).then(res => {
            let role_data       = res[0],
                event           = res[1];

            if (event === null) {
                return Promise.reject({ validation: 'Event not found' });
            }

            return { role_data, event };
        });
    },
    // ❗️❗️❗️TODO: need to be tested
    findOne: function (event_id, official_id, callback) {
        if(!event_id) return callback({
            text: 'No event identifier passed',
            type: 'ArgumentError'
        });

        if(!official_id) return callback({
            text: 'No official identifier passed',
            type: 'ArgumentError'
        });
            
        return Promise.all([
            findOfficial(official_id, event_id),
            this.findEvent(event_id, 'official'),
            findEmailContactProvider(event_id),
            this.clothing.getMemberClothes(official_id, event_id, 'official')
        ]).then(results => {
            return { official: results[0], event: results[1], email_contact_provider: results[2], clothes: results[3] };
        }).then(data => {
            callback(null, data);
        }).catch(err => {
            callback(err);
        });

        function findEmailContactProvider(eventID) {
            let sql =
                    `
                    SELECT eof.official_id as id, u.first, u.last 
                    FROM event_official eof
                        JOIN official of ON eof.official_id = of.official_id
                        JOIN "user" u ON u.user_id = of.user_id
                    WHERE eof.event_id = $1 AND eof.is_email_contact_provider = TRUE
                    `;
            return Db.query(sql, [eventID])
                .then(result => {
                    return result.rowCount == 1 ? result.rows[0] : {
                        id: 0,
                        first: null,
                        last: null
                    }
                });
        }

        async function findOfficial (officialID, eventID) {
            const sql =
                `SELECT 
                    eof.is_official, eof.is_staff,  eof.able_to_transport_others, eof.hotel_nights_required,
                    u.first, u.last, TO_CHAR(eof.departure_datetime, 'YYYY-MM-DD HH24:MI:SS') "departure_datetime", eof.need_hotel_room, eof.roommate_preference,
                    eof.schedule_availability, eof.travel_method, eof.bank_account_routing,
                    COALESCE(eof.rank, of.rank) "rank", 
                    of.is_official AS init_is_official, of.is_staff AS init_is_staff,
                    (eof.head_official IS TRUE) "head_official", eof.work_status, eof.bank_account_number, of.city, 
                    of.zip, of.advancement, of.address, of.state, u.gender, 
                    eof.additional_restrictions, eof.payment_option, eof.security_pin, of.region, of.aau_region, 
                    u.phone_mob, eof.event_official_id, eof.rating, eof.schedule_name, eof.deleted,
                    eof.is_email_notifications_receiver,
                    eof.is_email_contact_provider,
                    eoh.comment as reason,
                    TO_CHAR(eoh.created::TIMESTAMPTZ AT TIME ZONE e.timezone, 'MM/DD/YYYY, HH12:MI am') withdrawal_date,
                    of.special_sizing_requests,
                    of.usav_num,
                    of.aau_number,
                    TO_CHAR(of.birthdate, 'MM/DD/YYYY') AS birthdate,
                    of.emergency_contact_name,
                    of.emergency_contact_relationship,
                    of.emergency_phone,
                    of.checkin_barcode,
                    u.email,
                    eof.arbiterpay_username,
                    eof.arbiterpay_account_number,
                    eof.rq_pay_username,
                    EXISTS(
                        SELECT 1
                        FROM event_official_schedule eos
                        WHERE eos.event_official_id = eof.event_official_id
                            AND eos.published = TRUE
                            AND eof.work_status = $3
                        LIMIT 1
                    ) "is_assign_match",
                    oar.official_additional_role_id AS official_additional_role_id,
                    (
                        SELECT coalesce(array_to_json(array_agg(row_to_json(official_scan_history))), '[]'::json)
                        FROM (
                            SELECT
                                barcode,
                                action_type,
                                TO_CHAR(created::TIMESTAMPTZ AT TIME ZONE e.timezone, 'YYYY/MM/DD HH12:MI:SS am') "date_scanned"
                            FROM "official_scan_history"
                            WHERE barcode = of.checkin_barcode
                                AND event_id = e.event_id
                        ) official_scan_history
                    ) AS official_scan_barcode_history
                FROM official of
                INNER JOIN event_official eof
                    ON eof.official_id = of.official_id
                    AND eof.event_id = $2
                LEFT JOIN "user" u
                    ON u.user_id = of.user_id
                LEFT JOIN event_official_history eoh
                    ON eoh.event_official_id = eof.event_official_id
                LEFT JOIN "event" e
                    ON eof.event_id = e.event_id
                LEFT JOIN "event_official_additional_role" eoar
                    ON eoar.event_official_id = eof.event_official_id
                LEFT JOIN "official_additional_role" oar
                    ON oar.official_additional_role_id = eoar.official_additional_role_id
                WHERE of.official_id = $1`;

            const {rows: [official] = {}} = await Db.query(sql, [officialID, eventID, OfficialsService.payout.WORK_STATUS.APPROVED])

            if(official.checkin_barcode) {
                official.entry_qr_url =
                    OfficialsService.entryQRCode.getEntryQRCodeLink(eventID, official.checkin_barcode);
            }

            return official;
        }
    },
    findEvent: async function(eventID, member) {
        const query = squel.select()
            .field('e.name')
            .field('e.date_start')
            .field('e.date_end')
            .field(`(DATE_PART('day', e.date_end - e.date_start) +1)`, 'days_count')
            .field(`ARRAY(
                SELECT TO_CHAR(dd, 'MM/DD')
                FROM
                    GENERATE_SERIES (e.date_start, e.date_end, '1 day'::INTERVAL) dd
            )`, 'days_dates')
            .field('e.official_qr_code_enable')
            .field('e.official_additional_role_enable')
            .from('event', 'e')
            .where('e.event_id = ?', eventID);

        if (member === CheckinService.ROLE.OFFICIAL) {
            query.field('e.official_payment_method', 'available_payment_methods');
        } else if (member === CheckinService.ROLE.STAFF) {
            query.field('e.staff_payment_method', 'available_payment_methods');
        }

        const { rows: [event] } = await Db.query(query);

        if (event && event.available_payment_methods) {
            event.payment_methods = CheckinService.setPaymentOptionsAvailability(event.available_payment_methods);

            delete event.available_payment_methods;
        };

        return event;
    },
 
    // ❗️❗️❗️TODO: need to be tested and 🔧refactored
    updateRegData: function (eventID, officialID, headName, data) {
        let validationResult = SchInfoSchema.validate(data, {
            abortEarly: false
        });

        if(validationResult.error) {
            return Promise.reject({ validationErrors: validationResult.error.details });
        }

        return co(function* () {
            const eventOfficialFields = _.omit(data , ['notes', 'official_additional_role_id']);
            
            let official;
            let tr = yield (Db.begin());
            
            if(!_.isEmpty(eventOfficialFields)) {
                let sql = squel.update().table('event_official', 'eof')
                    .with(
                        'person',
                        squel.select()
                            .field('u.email')
                            .from('official', 'of')
                            .join('user', 'u', 'u.user_id = of.user_id')
                            .where('of.official_id = ?', officialID)
                    )
                    .with(
                        'initial',
                        squel.select()
                            .field('eof.rank').field('eof.work_status')
                            .from('event_official', 'eof')
                            .where('eof.event_id = ?', eventID)
                            .where('eof.official_id = ?', officialID)
                    )
                    .setFields(eventOfficialFields)
                    .where('eof.event_id = ?', eventID)
                    .where('eof.official_id = ?', officialID)
                    .where('eof.deleted IS NULL')
                    .returning(
                        `(SELECT ROW_TO_JSON("d")
                  FROM (
                    SELECT "eof".event_official_id, "person".* 
                    FROM "person"
                   ) "d"
                ) "person", (
                    SELECT ROW_TO_JSON("d")
                    FROM (
                        SELECT "initial".* FROM "initial"
                    ) "d"
                ) "initial"`
                    );
                
                if(data.work_status && data.work_status != 'approved') {
                    sql.set('is_email_notifications_receiver', false);
                    sql.set('is_email_contact_provider', false);
                    sql.set('head_official', false);
                }
                
                official = yield (tr.query(sql).then(result => {
                    return result.rows[0];
                }));
                
                if (_.isEmpty(official)) {
                    yield (tr.rollback());
                    return Promise.resolve({ validation: 'Official not found' });
                }
                
                // ❗️We need to change rank in the "official" table
                if (data.rank && (official.initial.rank !== data.rank)) {
                    loggers.debug_log.verbose(`Updating official's rank`);
                    yield (tr.query(
                        `UPDATE "official"
                     SET "rank" = $2
                     WHERE "official_id" = $1`,
                        [officialID, data.rank]
                    ))
                }
                
                // ❗️We need to copy "rank" and "security_pin" values to "event_official" if accepted
                if ((data.work_status === 'approved') && (official.initial.work_status !== data.work_status)) {
                    loggers.debug_log.verbose(`Updating event official's rank and security pin`);
                    
                    let res = yield (tr.query(
                        `UPDATE "event_official" eof
                     SET "security_pin" = o."security_pin",
                        "rank" = o."rank"
                     FROM (
                         SELECT o."rank", o."security_pin", o."official_id"
                         FROM "official" o 
                         WHERE o."official_id" = $1
                     ) "o"
                     WHERE eof."event_official_id" = $2
                        AND eof."event_id" = $3
                        AND eof."official_id" = o."official_id"`,
                        [officialID, official.person.event_official_id, eventID]
                    ));
                    
                    if (res.rowCount === 0) {
                        loggers.errors_log.error('No rows found');
                        yield (tr.rollback());
                        throw new Error('Internal Error');
                    }
                }
                
                if (data.notes) {
                    // 🔧TODO: save to "official_history" table, which does not exist
                }
            }
            
            if (data.official_additional_role_id) {
                loggers.debug_log.verbose(`Updating official's additional role`);
                yield (tr.query(
                    `UPDATE "event_official_additional_role"
                     SET "official_additional_role_id" = $1
                     WHERE "event_official_id" = (
                        SELECT event_official_id FROM "event_official"
                        WHERE official_id = $2 AND event_id = $3
                     )`,
                    [data.official_additional_role_id, officialID, eventID]
                ))
            }
            
            yield (tr.commit());

            if (data.work_status && (data.work_status !== 'pending') && headName) {
                yield this.sendRoleNotification(
                    data.work_status, AEMService.OFFICIAL_GROUP, official.person.event_official_id, eventID
                );
            }
        }.bind(this));
    },

    sendRoleNotification (work_status, group, event_official_id, eventID) {
        let rolePrefix = null;

        if(group === AEMService.STAFF_GROUP) {
            rolePrefix = AEMService.TRIGGER_ROLE_PREFIX.STAFF;
        } else if(group === AEMService.OFFICIAL_GROUP) {
            rolePrefix = AEMService.TRIGGER_ROLE_PREFIX.OFFICIAL;
        }

        let template = AEMService.triggers.getRoleTmplTypeForWorkStatus(rolePrefix, work_status);

        let params = { event_official_id };

        if(work_status === 'withdrew') {
            params.isWithdrewNotification = true;
        }

        return AEMSenderService.sendTriggerNotification(group, template, eventID, params)
            .catch(() => {});
    },

    // ❗️❗️❗️TODO: need to be tested
    updateRegDataGroup: function (eventID, officialsList, headName, data) {
        if (!Number.isInteger(eventID)) {
            return Promise.reject({ validation: 'Event ID should be an integer' });
        }

        let numOfficials = officialsList.filter(id => Boolean(parseInt(id, 10)));

        if (numOfficials.length !== officialsList.length) {
            return Promise.reject({ validation: 'Expecting Official Identifiers to be integers' });
        }

        let errors = [];

        return officialsList.reduce((prev, officialID) => 
            prev.then(() => 
                this.updateRegData(eventID, officialID, headName, data)
                .catch(err => {
                    errors.push(err);
                })
            )
        , Promise.resolve())
        .then(() => {
            if (errors.length) {
                return Promise.reject({ validationErrors: errors });
            }
        });

    },

    // ❗️❗️❗️TODO: need to be tested
    regInfoChangeNotify: async function (eventID, officialID, official, baseUrl) {
        let isOfficial = official.current.is_official || official.initial.is_official;

        let credentials = await (Promise.all([
            (isOfficial)
                ? this.getHOEmails(eventID)
                : Promise.resolve([]),
            this.getEventCredentials(eventID)
        ]));

        let eventData = credentials[1];

        let officialDetailsLink = baseUrl + `/api/common/event/${eventID}/official/${officialID}`;

        let emailReceivers;

        if (!_.isEmpty(credentials[0])) {
            emailReceivers = credentials[0];
        } else if (!!eventData.email) {
            emailReceivers = [eventData.email];
        } else {
            ErrorSender.defaultError( new Error(`no receivers for official reg info update. Info: ${emailReceivers}`) );
            return;
        }

        /* NOTE: might be moved to AEMSenderService.__findNotificationReceivers__ */
        /* Another way is to pass that data to notification sender as a new last parameter */
        let difference = CheckinService.getChangedFieldsDifference(
            official.initial, official.current, eventData.dates
        );

        if (difference.length === 0) {
            loggers.debug_log.verbose('No difference');
            return;
        }

        const emailSendPromises = emailReceivers.map(to => EmailService.renderAndSend({
            template    : 'official/checkin_update',
            layout      : 'official/layout',
            data        : {
                eventDates      : eventData.dates,
                eventName       : eventData.name,
                difference      : difference,
                official        : official.general,
                detailsLink     : officialDetailsLink,
                formatValue     : function (val) {
                    /* jshint eqnull:true */
                    if (val == null || val === '') {
                        return 'N/A';
                    }

                    if (_.isBoolean(val)) {
                        return val ? 'Yes' : 'No';
                    }

                    return val;
                },
                baseUrl         : baseUrl
            },
            from        : 'SportWrench <<EMAIL>>',
            replyto     : `"${official.general.first} ${official.general.last}" <${official.general.email}>`,
            subject     : `${eventData.short_name}: Official's Reg Info Change`,
            to
        }).catch(ErrorSender.emailSendingError));

        await Promise.all(emailSendPromises);
        
    },

    // ❗️❗️❗️TODO: need to be tested
    getEventCredentials (eventID) {
        return Db.query(
            `SELECT ARRAY( 
                     SELECT TO_CHAR(dd, 'Dy, Mon DD') 
                     FROM GENERATE_SERIES (e.date_start, e.date_end, '1 day'::INTERVAL) "dd"
                 ) "dates",
                 COALESCE(NULLIF(e.notify_emails, ''), e.email) "email",
                 e.long_name "name",
                 e.name "short_name"
             FROM "event" e 
             WHERE e.event_id = $1`,
            [eventID]
        ).then(result => result.rows[0]);
    },

    // ❗️❗️❗️TODO: need to be tested
    getHOEmails: function (eventID) {
        return Db.query(
            `SELECT 
                FORMAT('"%s %s" <%s>', u.first, u.last, u.email) "email"
             FROM "event_official" eof 
             INNER JOIN "official" of 
                ON of.official_id = eof.official_id 
             INNER JOIN "user" u 
                ON u.user_id = of.user_id
             WHERE eof.event_id = $1 
                AND eof.head_official IS TRUE
                AND eof.is_email_notifications_receiver IS TRUE
                AND eof.deleted IS NULL`,
            [eventID]
        ).then(({ rows }) => rows.map(({ email }) => email));
    },
    // ❗️❗️❗️TODO: need to be tested
    isHeadOfficial: function (eventID, officialID) {
         return Db.query(
            `SELECT eof.head_official 
             FROM event_official eof 
             WHERE event_id = $1 
                AND official_id = $2
                AND eof.head_official IS TRUE`,
            [eventID, officialID]
        ).then(result => (result.rows.length > 0));
    },

    results: OfficialsResultsService,

    checkin: CheckinService,

    profile: ProfileService,

    manage: EventManageService,

    assignments: AssignmentsService,

    clothing: ClothingService,

    sanctioningCheckUpdate: SanctioningCheckUpdateService,

    eventOfficialHistory: EventOfficialHistoryService,

    rate: EventOfficialRateService,

    payout: EventOfficialPayoutService,

    additionalPayment: EventOfficialAdditionalPaymentService,

    entryQRCode: EntryQRCodeService,

    officialEvents: OfficialEventsList,

    get_export_query: function (officialsList, eventID) {

        let stringifiedList;

        if (Array.isArray(officialsList)) {
            stringifiedList = swUtils.numArrayToString(officialsList, null, true);
        } else if (officialsList === null) {
            stringifiedList = null;
        }
        let query =
                `SELECT 
           TO_CHAR(of.created::TIMESTAMPTZ AT TIME ZONE e.timezone, 'MM/DD/YYYY HH12:MI AM') "Created", 
           TO_CHAR(eof.created::TIMESTAMPTZ AT TIME ZONE e.timezone, 'MM/DD/YYYY HH12:MI AM') "Registered",
           u.first "First", 
           u.last "Last", 
           u.email "Email", 
           u.phone_mob "Phone", 
           TO_CHAR(of.birthdate::TIMESTAMPTZ AT TIME ZONE e.timezone, 'MM/DD/YYYY HH12:MI AM') "Birthdate", 
           of.aau_number "AUU Number",
           of.city "City", of.state "State", of.zip "Zip",
           of.address "Address", 
           of.rank "Certification level", 
           of.region "USAV region", 
           eof.security_pin "Security Pin", 
           (CASE WHEN eof.able_to_transport_others IS TRUE THEN 'Y' ELSE 'N' END) "Able to transport others", 
           eof.additional_restrictions "Additional restrictions",                    
           TO_CHAR(eof.departure_datetime, 'YYYY-MM-DD HH24:MI:SS') "Departure datetime", 
           (CASE WHEN eof.need_hotel_room IS TRUE THEN 'Y' ELSE 'N' END) "Need hotel room", 
           eof.roommate_preference "Roommate preference", 
           eof.schedule_availability "Schedule availability", 
           eof.hotel_nights_required "Hotel Nights Required",
           eof.travel_method "Travel method",      
           (CASE WHEN eof.head_official IS TRUE THEN 'Y' ELSE 'N' END) "Head official", 
           ( CASE 
                WHEN NULLIF(eof.deleted, NULL) IS NULL THEN (INITCAP(eof.work_status::TEXT))
                ELSE 'withdrawn'
                END
            ) "Work status", 
           ((e.date_end::DATE - e.date_start::DATE) + 1) "event_days_count", 
           eof.payment_option "Payment option",
           eof.rq_pay_username "RQ Pay Username",
           e.officials_hotel_comp "Hotel dates"
        FROM event_official eof 
        LEFT JOIN official of 
          ON eof.official_id = of.official_id 
        INNER JOIN event e 
          ON eof.event_id = e.event_id     
        LEFT JOIN "user" u 
          ON u.user_id = of.user_id 
        WHERE eof.event_id = ${eventID} 
            ${stringifiedList ? `AND eof."official_id" IN (${stringifiedList})` : ''}
        AND eof.is_official IS TRUE`;

        return query;
    },

    removeProperty(property) {
      return function(item) {
          return _.omit(item, [property]);
      };
    },

    swapValue(v) {
        const _v = v.split('-');

        [_v[0], _v[1]] = [_v[1], _v[0]];

        return _v.join('-');
    },

    generateResults(o) {
      let result = '';
      const _o = _.omit(o, 'winner');

      _.forIn(_o, (value) => {
          const winner = o.winner && o.winner.toString();
          const _value = winner === "1" ? value : this.swapValue(value);

          result += `${_value}, `;
      });

      return result ? `(${result.slice(0, -2)})` : null;
    },

    updateMatchInfo(match, show_link, edit_mode) {
        if (match.results) {
            const _results = _.pick(match.results, ['winner', 'set1','set2','set3','set4','set5']);

            match.results = this.generateResults(_results);
        }

        return Object.assign({}, match, { show_scored_link: show_link, edit_scored_link_mode: edit_mode });
    },
    /**
     * entered_scores_recently has 3 states
     *
     * NULL     - scores doesn't entered
     * TRUE     - scores entered recently (less then 5 minutes ago)
     * FALSE    - scores entered more then 5 minutes ago
     *
     */
    formatEvent(event) {
        if (_.isEmpty(event)) {
            return null;
        }

        const { assignments } = event;

        for (let key in assignments) {
            let assignment = assignments[key];

            if (!assignment.length) {
                continue;
            }

            const _assignment = assignment.map((match, index, arr) => {
                const isFirstElement = index === 0;
                const prevElement = arr[index - 1];

                if (!isFirstElement && _.isNull(prevElement.entered_scores_recently)) {
                    return this.updateMatchInfo(match, false, false);
                }

                if (_.isNull(match.entered_scores_recently)) {
                    return this.updateMatchInfo(match, true, false);
                }

                return match.entered_scores_recently
                    ? this.updateMatchInfo(match, true, true)
                    : this.updateMatchInfo(match, false, false);
            });

            assignments[key] =  _assignment.map(this.removeProperty('entered_scores_recently'));
        }

        return event;
    },
    getStaffInfo(eventID, staffID) {
        const sql = squel.select()
            .from('official', 'of')
            .field('eof.is_staff')
            .field('eof.staff_work_status', 'work_status')
            .field('u.first')
            .field('u.last')
            .field('eof.staff_deleted IS NOT NULL', 'staff_deleted')
            .field('eof.staff_payment_option', 'payment_option')
            .field('eof.bank_account_number')
            .field('eof.bank_account_routing')
            .field('eof.travel_method')
            .field('eof.roommate_preference')
            .field('eof.schedule_availability')
            .field(`TO_CHAR(eof.departure_datetime, 'YYYY-MM-DD HH24:MI:SS')`, 'departure_datetime')
            .field(`TO_CHAR(eof.staff_arrival_datetime, 'YYYY-MM-DD HH24:MI:SS')`, 'arrival_datetime')
            .field('eof.additional_restrictions')
            .field('eof.hotel_nights_required')
            .field('eof.need_hotel_room')
            .field('of.special_sizing_requests')
            .field('COALESCE(eof.rank, of.rank)', 'rank')
            .field('of.usav_num')
            .field('of.aau_number')
            .field('of.region')
            .field('of.aau_region')
            .field('u.gender')
            .field(`TO_CHAR(of.birthdate, 'MM/DD/YYYY')`, 'birthdate')
            .field('of.address')
            .field('of.city')
            .field('of.state')
            .field('of.zip')
            .field('of.emergency_contact_name')
            .field('of.emergency_phone')
            .field('of.emergency_contact_relationship')
            .field('eof.is_official')
            .field('u.email')
            .field('u.phone_mob')
            .field('eof.arbiterpay_username')
            .field('eof.arbiterpay_account_number')
            .join('event_official', 'eof', 'eof.official_id = of.official_id')
            .left_join('user', 'u', 'u.user_id = of.user_id')
            .where('eof.official_id = ?', staffID)
            .where('eof.event_id = ?', eventID);

        return Db.query(sql, [staffID, eventID]).then(({ rows }) => rows[0]);
    },
    updateStaffInfo(eventID, staffID, data) {
        const sql = squel.update()
            .table('event_official', 'eof')
            .setFields(data)
            .with('event_official_before_update',
                squel.select()
                    .from('official', 'of')
                    .field('eof.staff_work_status')
                    .join('event_official', 'eof', 'eof.official_id = of.official_id')
                    .left_join('user', 'u', 'u.user_id = of.user_id')
                    .where('eof.official_id = ?', staffID)
                    .where('eof.event_id = ?', eventID)
            )
            .where('eof.event_id = ?', eventID)
            .where('eof.official_id = ?', staffID)
            .where('eof.staff_deleted IS NULL')
            .returning(`
                eof.staff_work_status work_status, 
                eof.event_official_id,
                (SELECT staff_work_status FROM event_official_before_update) old_work_status
            `);

        return Db.query(sql).then(({ rows }) => rows && rows[0]);
    },
    /**
     * Update additional restrictions for official
     * @param eventID
     * @param officialID
     * @param additionalRestrictions
     * @returns {Promise}
     */
    updateAdditionalRestrictions(eventID, officialID, additionalRestrictions) {
        const sql = knex('event_official')
            .update({ additional_restrictions: additionalRestrictions })
            .where('event_id', eventID)
            .where('official_id', officialID);

        return Db.query(sql);
    },
    /**
     *
     * @param {number} eventID
     * @param {Array|number} staffersIDs
     * @param {String} clientTimezone
     */
    async getStaffExportQuery(eventID, staffersIDs, clientTimezone) {
        const _ids = Array.isArray(staffersIDs)
            ? `{${staffersIDs.join(', ')}}`
            : `{${staffersIDs}`;

        const query = squel.select().from('event_official', 'eo')
            .field(`TO_CHAR(o.created::TIMESTAMPTZ AT TIME ZONE e.timezone, 'MM/DD/YYYY HH12:MI AM')`, '"Created"')
            .field(`TO_CHAR(eo.created::TIMESTAMPTZ AT TIME ZONE e.timezone, 'MM/DD/YYYY HH12:MI AM')`, '"Registered"')
            .field(`u.first`, '"First"')
            .field(`u.last`, '"Last"')
            .field(`u.email`, '"Email"')
            .field(`u.phone_mob`, '"Phone"')
            .field(`o.city`, '"City"')
            .field(`o.state`, '"State"')
            .field(`o.zip`, '"Zip"')
            .field(`o.address`, '"Address"')
            .field(`o.rank`, '"Certification level"')
            .field(`o.region`, '"USAV region"')
            .field(`eo.security_pin`, '"Security Pin"')
            .field(`(CASE WHEN eo.able_to_transport_others IS TRUE THEN 'Y' ELSE 'N' END)`, '"Able to transport others"')
            .field(`eo.additional_restrictions`, '"Additional restrictions"')
            .field(`TO_CHAR(eo.departure_datetime, 'YYYY-MM-DD HH24:MI:SS')`, '"Departure datetime"')
            .field(`TO_CHAR(eo.staff_arrival_datetime::TIMESTAMPTZ AT TIME ZONE '${clientTimezone}', 'YYYY-MM-DD HH24:MI:SS')`, '"Arrival datetime"')
            .field(`(CASE WHEN eo.need_hotel_room IS TRUE THEN 'Y' ELSE 'N' END)`, '"Need hotel room"')
            .field(`eo.roommate_preference`, '"Roommate preference"')
            .field(`eo.schedule_availability`, '"Schedule availability"')
            .field(`eo.hotel_nights_required`, '"Hotel Nights Required"')
            .field(`eo.travel_method`, '"Travel method"')
            .field(`(CASE WHEN eo.head_official IS TRUE THEN 'Y' ELSE 'N' END)`, '"Head official"')
            .field(`(
                    CASE 
                        WHEN NULLIF(eo.deleted, NULL) IS NULL THEN (INITCAP(eo.staff_work_status::TEXT))
                        ELSE 'withdrawn'
                    END
                )`
            , '"Work status"')
            .field(`((e.date_end::DATE - e.date_start::DATE) + 1)`, 'event_days_count')
            .field(`eo.staff_payment_option`, '"Payment option"')
            .field(`e.officials_hotel_comp`, '"Hotel dates"')
            .join('official', 'o', 'o.official_id = eo.official_id')
            .join('event', 'e', 'e.event_id = eo.event_id')
            .join('user', 'u', 'u.user_id = o.user_id')
            .where('eo.is_staff IS TRUE')
            .where('eo.staff_deleted IS NULL')
            .where('eo.event_id = ?', eventID)
            .where('eo.official_id = ANY(?)', _ids);

        return query.toString();
    },
    sendRoleNotificationFilter(updatedStaffer) {
        return updatedStaffer && updatedStaffer.work_status !== updatedStaffer.old_work_status;
    },
};

Object.defineProperty(OfficialsService, 'SCHEDULE_APP_LINK', {
    value           : SCHEDULE_APP_LINK,
    writable        : false,
});

module.exports = OfficialsService;
