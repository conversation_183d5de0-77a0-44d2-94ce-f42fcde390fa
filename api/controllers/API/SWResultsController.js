'use strict'

/**
 * Controller for SW Results - mobile page to enter match results (by Officials)
 *
 */
const moment = require('moment-timezone');
const JSON_FORMAT_VERSION = 3;
const CREATED_BY = 'ESW-Results';
const swUtils = require('../../lib/swUtils');

module.exports = {

    // GET /api/swr/check-pin/:event/:pin
    check_pin: function (req, res) {
        var eventId = parseInt(req.params.event, 10),
            pin = req.params.pin;

        if(!eventId) return res.status(200).json({ 'auth': false, err: 'Invalid event id' });
        // Admin access
        // if ((eventId === 21) && (pin === '1776')) return res.status(200).json({'auth': true, 'barcode': false});

        //if (eventId == '1') {
        //    //DEMO
        //    sails.log('----demo----');
        //    params = [pin];
        //    return res.status(200).json({'auth': true});
        //    //return res.status(200).json({'auth': false, err:'Demo Score is not active. Please use real event id.'});
        //}

        var params = [pin, eventId];
        var query = 
            `SELECT  
               e.event_id, eof.official_id, eof.event_official_id, 
               e.score_entry_allowed,
               e.score_entry_live,
               e.has_match_barcodes, 
               e.timezone,
               COALESCE(e.require_match_end_time, eo.require_match_end_time, FALSE) require_match_end_time, 
               (e.admin_security_pin = $1) "show_all_matches" 
            FROM "event" e  
            LEFT JOIN "event_official" eof
              ON e.event_id = eof.event_id 
              AND eof.work_status = 'approved'
              AND eof.security_pin = $1 
            LEFT JOIN "event_owner" eo
                ON eo.event_owner_id = e.event_owner_id
            WHERE e.event_id = $2  
                AND ( 
                    eof.event_official_id IS NOT NULL 
                    OR ( 
                        eof.event_official_id IS NULL AND e.admin_security_pin = $1 
                    ) 
                )`;
        Db.query(query, params).then(function (result) {
            if(result.rowCount > 1) {
                res.status(200).json({
                    auth: false,
                    err: 'Multiple Users Found'
                });
            } else {
                let row = result.rows[0];

                if(_.isEmpty(row)) {
                    res.status(200).json({
                        auth: false,
                        err: 'Access denied. No official found or admin password is incorrect' 
                    }); 
                } else {

                    if (!row.score_entry_allowed) {
                        res.status(200).json({
                            auth: false,
                            err: `Access denied. This event doesn't allow score entry`
                        });
                    } else {

                        //if (eventId === 1) {
                        //    row.event_id = 1;
                        //}
                        if (eventId !== 72 && eventId !== 67) {
                            row.show_all_matches = false;
                        }
                        // Show Matches list for AJ's event
                        //if (eventId === 72) {
                        //    row.show_all_matches = true;
                        //    //row.has_match_barcodes = false;
                        //}
                        res.status(200).json({auth: true, barcode: row.has_match_barcodes, data: row});
                    }
                }
            }
        }).catch(err => {
            res.status(200).json({ auth: false, err: err })
        })
    },


    // GET /api/swr/get-event-courts/:event/:day
    get_courts: function (req, res) {
        var event_id = +req.param('event');
        if (!event_id) {
            loggers.errors_log.refError(req, 'Invalid event_id');
            return res.status(400).json({error: 'Event is not specified.'});
        }
        
        var day = req.param('day');
        if (!event_id) {
            loggers.errors_log.refError(req, 'Invalid day');
            return res.status(400).json({error: 'day is not specified.'});
        }

        var query =
            ' SELECT mt.court_id, c.name court_name ' +
            ' FROM matches mt ' +
            ' LEFT JOIN courts c ON mt.court_id = c.uuid '+
            ' WHERE mt.event_id = $1 ' +
            ' AND to_char(mt.secs_start, \'YYYY-MM-DD\') = $2 ' +
            ' GROUP BY mt.event_id, mt.court_id, c.name, c.sort_priority ' +
            ' ORDER BY c.sort_priority ';

        Db.query(query, [event_id, day]).then(function (result) {
            res.status(200).json({ 'courts': result.rows });
        }).catch(err => {
            res.serverError(err);    
        })
    },

    // GET /api/swr/get-matches/:event/:day/:court
    get_matches: function(req, res) {
        var event_id = +req.param('event');
        if (!event_id) {
            loggers.errors_log.refError(req, 'Invalid event_id');
            return res.status(400).json({error: 'Event is not specified.'});
        }

        var day = req.param('day');
        if (!day) {
            loggers.errors_log.refError(req, 'Invalid day');
            return res.status(400).json({error: 'day is not specified.'});
        }

        var court = req.param('court');
        if (!swUtils.isUUID(court)) {
            loggers.errors_log.refError(req, 'Invalid court');
            return res.status(400).json({error: 'court is not specified.'});
        }

        const query = `
            SELECT m.match_id, m.display_name match_name, m.source, m.results, pb.pb_seeds,
                d.division_id, d.name division_name, d.short_name division_short_name,
                extract(epoch from m.secs_start)::BIGINT date_start,
                extract(epoch from m.secs_end)::BIGINT * 1000 date_end,
                extract(epoch from m.secs_finished)::BIGINT * 1000 date_finished,
                m.team1_roster_id,
                m.team2_roster_id,
                rt1.team_name team_1_name,
                rt2.team_name team_2_name,
                rtr.team_name team_ref_name,
                m.is_tie_breaker
            FROM matches m
            LEFT JOIN poolbrackets pb ON pb.uuid = m.pool_bracket_id
            LEFT JOIN division d ON d.division_id = m.division_id
            LEFT JOIN roster_team rt1 ON rt1.roster_team_id = m.team1_roster_id
            LEFT JOIN roster_team rt2 ON rt2.roster_team_id = m.team2_roster_id
            LEFT JOIN roster_team rtr ON rtr.roster_team_id = m.ref_roster_id
            WHERE m.event_id = $1
            AND m.court_id = $3
            AND to_char(m.secs_start, 'YYYY-MM-DD') = $2
            ORDER BY m.secs_start`;

        Db.query(query, [event_id, day, court]).then(function (result) {
            if (result.rows && result.rows.length) {
                result.rows.forEach(function (match) {
                    let results;
                    if (match.results) {
                        results = match.results;
                        var winner = 0;
                        var scores = '';

                        if (results && results.winner) {
                            // fix incorrect winner when match is a tie
                            if(
                                results.team1 &&
                                results.team2 &&
                                results.team1.sets_won &&
                                results.team2.sets_won &&
                                results.team1.sets_won === results.team2.sets_won
                            ) {
                                results.winner = 0;
                            }
                            winner = results.winner;
                            if (results['team' + winner] && results['team' + winner].scores)
                                scores = results['team' + winner].scores;

                            match.res_team1_roster_id = results.team1.roster_team_id;
                            match.res_team2_roster_id = results.team2.roster_team_id;
                            match.res_winner = results.winner;

                            if (match.res_winner == 1) {
                                match.scores = results.team1.scores;
                            } else if (match.res_winner == 2) {
                                match.scores = results.team2.scores;
                            }
                        }
                    }
                    if (match.source && match.pb_seeds) {
                        var seeds = [];
                        try {
                            var seedsObj = JSON.parse(match.pb_seeds);
                            _.each(seedsObj, function (seed, key) {
                                seeds[parseInt(key, 10)] = JSON.parse(seed);
                            });
                        } catch (e) {}

                        try {
                            var source = JSON.parse(match.source);
                        } catch (e) {}

                        var seed, newPool;
                        seed = newPool = 0;
                        if (source && source['team1'] && source['team1']['type'] && source['team1']['type'] == 5 && source['team1']['seed']) {
                            seed = source['team1']['seed'];
                            newPool = seeds[seed];
                            if (newPool) {
                                match.team1_pool_name = newPool.name;
                                match.team_1_name = match.team_1_name || match.team1_pool_name || match.source.team1.name;
                            }
                        }

                        if (source && source['team2'] && source['team2']['type'] && source['team2']['type'] == 5 && source['team2']['seed']) {
                            seed = source['team2']['seed'];
                            newPool = seeds[seed];
                            if (newPool) {
                                match.team2_pool_name = newPool.name;
                                match.team_2_name = match.team_2_name || match.team2_pool_name || match.source.team2.name;
                            }
                        }

                        if (source && source['ref'] && source['ref']['type'] && source['ref']['type'] == 5 && source['ref']['seed']) {
                            seed = source['ref']['seed'];
                            newPool = seeds[seed];
                            if (newPool) {
                                match.ref_pool_name = newPool.name;
                                match.team_ref_name = match.team_ref_name || match.ref_pool_name || match.source.ref.name;
                            }
                        }

                        delete match.team1_pool_name;
                        delete match.team2_pool_name;
                        delete match.ref_pool_name;
                        //delete match.results;
                        delete match.source;
                        delete match.pb_seeds;
                        match.results = results;
                    }
                });
            }    

            res.status(200).json({'matches': result && result.rows});
        }).catch(err => {
            res.serverError(err);  
        })
    },


    // GET /api/swr/check-match/:event/:barcode/:match
    // to get match info by barcode pass :match = 0
    // to get match info by match id pass :barcode = 0
    check_match: function (req, res) {

        var eventId = req.params.event;
        var matchId = req.params.match;
        var barcode = parseInt(req.params.barcode, 10);

        if (Number.isNaN(barcode)) {
            return res.validation('Invalid barcode');
        }

        if(barcode <= 0 && matchId && !swUtils.isUUID(matchId)) {
            return res.validation('Invalid match id');
        }

        var params = [eventId];

        var subquery;

        /* jshint -W018 */
        if (barcode > 0) {
            subquery = '  LEFT JOIN matches m ON m.event_id = e.event_id AND m.match_barcode = $2 ';
            params.push(barcode);
        } else if (!!matchId !== false) {
            subquery = '  LEFT JOIN matches m ON m.event_id = e.event_id AND m.match_id = $2 ';
            params.push(matchId);
        } else {
            return res.validation('Expecting match id or match barcode to be present')
        }

        // Getting Official by PIN
        var query = `
            SELECT 
                e.score_entry_allowed,
                e.score_entry_live,
                d.short_name division_short_name, 
                m.display_name, c.name court_name, 
                extract(epoch from m.secs_start)::BIGINT date_start, 
                extract(epoch from m.secs_end)::BIGINT date_end, 
                extract(epoch from m.secs_finished)::BIGINT date_finished, 
                m.secs_start, m.secs_end, m.secs_finished, 
                (now() AT TIME ZONE e.timezone) - INTERVAL \'2 minutes\' "recent_time", 
                m.team1_roster_id, m.team2_roster_id, m.match_barcode, 
                rt1.team_name team1_name, rt2.team_name team2_name, 
                r.data results_data, r.moment results_moment, pb.settings, m.is_tie_breaker,
                e.require_match_end_time, 
                e."swb_settings"->>'disable_score_entry_validation' "disable_score_entry_validation",
                (d."swb_settings" ->> 'TieBreakerPoints')::INTEGER "tie_breaker_points",
                (e.event_id IN (72, 67, 19018) and pb.settings::JSON->>'SetCount' = '2' and pb.settings::JSON->>'PlayAllSets' = 'true') hide_winner 
               FROM event e `
            + subquery + `
               LEFT JOIN results r ON r.match_barcode = m.match_barcode AND r.event_id = $1 
               LEFT JOIN roster_team rt1 ON rt1.roster_team_id = m.team1_roster_id 
               LEFT JOIN roster_team rt2 ON rt2.roster_team_id = m.team2_roster_id 
               LEFT JOIN division d ON d.division_id = m.division_id 
               LEFT JOIN courts c ON c.uuid = m.court_id 
               LEFT JOIN poolbrackets pb ON pb.uuid = m.pool_bracket_id 
                WHERE e.event_id = $1 
               ORDER BY r.moment DESC  
               LIMIT 1 `;

        //return cb(query);
        Db.query(query, params).then(function (result) {
            if (result.rows.length === 0) {
                return res.status(200).json({success:false, message: 'Event id is invalid'});
            }
            let row = result.rows[0];
            if (row.score_entry_allowed !== true) {
                return res.status(200).json({success: false, message: 'This match or event does not allow to enter scores'});
            }

            if (row.team1_roster_id && row.team2_roster_id) {
                return res.status(200).json(row);
            } else {
                if (row.match_barcode) {
                    return res.status(200).json({success: false, message: 'This match has no teams assigned yet'});
                } else {
                    return res.status(200).json({success: false, message: 'Invalid Barcode or match not found'});
                }
            }
        }).catch(err => {
            res.status(200).json({error: err.toString() + ' SQL:' + query});
        })
    },

    // POST /api/swr/post-results
    post_results: function (req, res) {


        var eventId = req.body.event_id;

        //var eventOfficialId = req.body.event_official_id;
        var match_barcode = req.body.match_barcode;
        var winner = req.body.winner;
        //var winnning_team = req.body.winnning_team;
        var set1 = req.body.set1;
        var set2 = req.body.set2;
        var set3 = req.body.set3;
        var set4 = req.body.set4;
        var set5 = req.body.set5;
        var date_finished = req.body.date_finished*1000;
        var score_entry_live = req.body.score_entry_live;

        if (!set1) {
            return res.status(200).json({success: false, message: 'Invalid or empty scores posted'});
        }

        // Removing strings with 'undefined-undefined'
        if (set2 === 'undefined-undefined') set2 = undefined;
        if (set3 === 'undefined-undefined') set3 = undefined;

        var query = `SELECT uuid_generate_v4() uuid, 
                       e.score_entry_allowed,
                       e.score_entry_live,
                       e.timezone, 
                       e.require_match_end_time  
                     FROM event e 
                     WHERE e.event_id = $1 `;

        //return cb(query);
        Db.query(query, [eventId]).then(function (result) {
            var event = result.rows[0];
            if (event.score_entry_allowed !== true) {
                return res.status(200).json({success: false, message: 'This match or event does not allow to enter scores'});
            }
            var demoMode = (event.score_entry_live !== true) || (score_entry_live === 'false');

            var resultsData = {
                //winner: winner,
                set1: set1,
                //set2: set2,
                //set3: set3,
                official_id: req.body.official_id
            };
            if (winner) resultsData['winner'] = winner;
            if (set2) resultsData['set2'] = set2;
            if (set3) resultsData['set3'] = set3;
            if (set4) resultsData['set4'] = set4;
            if (set5) resultsData['set5'] = set5;

            var data = {
                data: {
                    id: event.uuid,
                    moment: moment().tz(event.timezone).format('YYYY-MM-DD HH:mm:ss'),
                    match_barcode: match_barcode,
                    data: JSON.stringify(resultsData),
                    is_dirty: 1,
                    event_id: eventId,
                    json_version: JSON_FORMAT_VERSION
                }                
            };

            if (event.require_match_end_time) {
                // Here come unix tsp, not need predefine moment date format
                date_finished
                    ? data.data.secs_finished = moment(date_finished).format('YYYY-MM-DD HH:mm:ss')
                    : moment().tz(event.timezone).format('YYYY-MM-DD HH:mm:ss')
            }

            var jsonData = JSON.stringify(data);

            //if (eventId == '1') {
            if (demoMode) {
                //DEMO
                sails.log('----demo----');                
                sails.log(data);
                data.score_entry_live = false;
                return res.status(200).json(data);
            }

            // Inserting result JSON into journal
            query =
                " INSERT INTO event_journal (event_id, table_name, method, created_by, data) " +
                "   VALUES ($1, 'results', 'insert', $2, $3) ";
            var params = [eventId, CREATED_BY, jsonData];

            return Db.query(query, params).then(function () {
                res.status(200).json(data);
            });

        }).catch(err => {
            res.status(200).json({error: err.toString() + ' SQL:' + query});
        })
    }
};
