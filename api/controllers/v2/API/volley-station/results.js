module.exports = {
    friendlyName: 'Submit match results for VolleyStation',
    description: 'Submit final match results to the event journal system',

    inputs: {
        eventId: {
            type: 'number',
            required: true,
            description: 'The ID of the event',
            min: 1
        },
        match_uuid: {
            type: 'string',
            required: true,
            description: 'UUID of the match',
            regex: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
        },
        is_final: {
            type: 'boolean',
            required: true,
            description: 'Whether this is a final result submission',
            custom: function(value) {
                if (value !== true) {
                    throw new Error('Currently only final results (is_final: true) are supported');
                }
                return true;
            }
        },
        date_finished: {
            type: 'string',
            required: true,
            description: 'ISO 8601 UTC timestamp when match finished',
            custom: function(value) {
                const date = new Date(value);
                if (isNaN(date.getTime())) {
                    throw new Error('Invalid ISO 8601 timestamp format');
                }
                return true;
            }
        },
        winner: {
            type: 'number',
            required: false,
            description: 'Winning team (1 or 2)',
            isIn: [1, 2]
        },
        results: {
            type: 'ref',
            required: true,
            description: 'Match results object with set scores',
            custom: function(value) {
                if (!value || typeof value !== 'object') {
                    throw new Error('Results must be an object');
                }
                
                if (!value.set1 || typeof value.set1 !== 'string') {
                    throw new Error('set1 is required and must be a string');
                }
                
                // Validate set score format (e.g., "25-23")
                const setPattern = /^\d{1,2}-\d{1,2}$/;
                for (const [key, score] of Object.entries(value)) {
                    if (key.match(/^set[1-5]$/) && score) {
                        if (!setPattern.test(score)) {
                            throw new Error(`${key} must be in format "XX-YY" (e.g., "25-23")`);
                        }
                    }
                }
                
                return true;
            }
        }
    },

    exits: {
        success: {
            statusCode: 200,
            description: 'Results submitted successfully'
        },
        badRequest: {
            statusCode: 400,
            description: 'Invalid request data'
        },
        notFound: {
            statusCode: 404,
            description: 'Match or event not found'
        },
        forbidden: {
            statusCode: 403,
            description: 'Score entry not allowed for this event'
        },
        serverError: {
            statusCode: 500,
            description: 'Internal server error'
        }
    },

    fn: async function (inputs, exits) {
        try {
            // Get event details and validate score entry is allowed
            const event = await VolleyStationService.getEventForResultSubmission(inputs.eventId);
            
            // Validate match exists and belongs to event
            const match = await VolleyStationService.validateMatchAccess(inputs.match_uuid, inputs.eventId);
            
            // Determine demo mode
            const demoMode = event.score_entry_live !== true;
            
            // Prepare results data object (similar to legacy format)
            const resultsData = {
                set1: inputs.results.set1
            };
            
            // Add optional fields
            if (inputs.winner) resultsData.winner = inputs.winner;
            if (inputs.results.set2) resultsData.set2 = inputs.results.set2;
            if (inputs.results.set3) resultsData.set3 = inputs.results.set3;
            if (inputs.results.set4) resultsData.set4 = inputs.results.set4;
            if (inputs.results.set5) resultsData.set5 = inputs.results.set5;
            
            // Submit results to event_journal
            const response = await VolleyStationService.submitMatchResults({
                eventId: inputs.eventId,
                matchBarcode: match.match_barcode,
                eventTimezone: event.timezone,
                dateFinished: inputs.date_finished,
                requireMatchEndTime: event.require_match_end_time,
                resultsData: resultsData,
                demoMode: demoMode
            });
            
            return exits.success(response);
            
        } catch (err) {
            loggers.errors_log.error('VolleyStation results submission error:', err);
            
            // Handle specific error types
            if (err.message.includes('not found') || err.message.includes('Event not found')) {
                return exits.notFound({ message: err.message });
            }
            
            if (err.message.includes('not allow') || err.message.includes('no teams assigned')) {
                return exits.forbidden({ message: err.message });
            }
            
            if (err.message.includes('Invalid') || err.message.includes('format')) {
                return exits.badRequest({ message: err.message });
            }
            
            // Generic server error
            return exits.serverError({ message: 'Unable to submit match results' });
        }
    }
};
