module.exports = {
    friendlyName: 'Submit match results for VolleyStation',
    description: 'Submit final match results to the event journal system',

    inputs: {
        eventId: {
            type: 'number',
            required: true,
            description: 'The ID of the event',
            min: 1
        },
        match_uuid: {
            type: 'string',
            required: true,
            description: 'UUID of the match',
            regex: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
        },
        is_final: {
            type: 'boolean',
            required: true,
            description: 'Whether this is a final result submission',
            custom: function(value) {
                if (value !== true) {
                    throw new Error('Currently only final results (is_final: true) are supported');
                }
                return true;
            }
        },
        date_finished: {
            type: 'string',
            required: true,
            description: 'ISO 8601 UTC timestamp when match finished',
            custom: function(value) {
                const date = new Date(value);
                if (isNaN(date.getTime())) {
                    throw new Error('Invalid ISO 8601 timestamp format');
                }
                return true;
            }
        },
        winner: {
            type: 'number',
            required: false,
            description: 'Winning team (1 or 2)',
            isIn: [1, 2]
        },
        results: {
            type: 'ref',
            required: true,
            description: 'Match results object with set scores',
            custom: function(value) {
                if (!value || typeof value !== 'object') {
                    throw new Error('Results must be an object');
                }
                
                if (!value.set1 || typeof value.set1 !== 'string') {
                    throw new Error('set1 is required and must be a string');
                }
                
                // Validate set score format (e.g., "25-23")
                const setPattern = /^\d{1,2}-\d{1,2}$/;
                for (const [key, score] of Object.entries(value)) {
                    if (key.match(/^set[1-5]$/) && score) {
                        if (!setPattern.test(score)) {
                            throw new Error(`${key} must be in format "XX-YY" (e.g., "25-23")`);
                        }
                    }
                }
                
                return true;
            }
        }
    },

    exits: {
        success: {
            statusCode: 200,
            description: 'Results submitted successfully'
        },
        badRequest: { // ?
            statusCode: 400,
            description: 'Invalid request data'
        },
        notFound: {
            statusCode: 404,
            description: 'Match or event not found'
        },
        forbidden: {
            statusCode: 403,
            description: 'Score entry not allowed for this event'
        }
    },

    fn: async function (inputs, exits) {
        try {
            const { eventId, match_uuid } = inputs;
            // Get event details and validate if score entry is allowed
            const [event, match] = await Promise.all([
                VolleyStationService.getEventForResultSubmission(eventId),
                VolleyStationService.getMatchForResultSubmission(match_uuid)
            ]);

            if (!event) {
                loggers.errors_log.error('Event not found for result submission:', inputs.eventId);
                return exits.notFound({ message: 'Event not found' });
            }
            if (!VolleyStationService.isScoreEntryAllowed(event)) {
                return exits.forbidden({ message: 'Score entry not allowed for this match or event' });
            }
            if (!match || match.event_id !== eventId) {
                loggers.errors_log.error('Match not found for result submission:', match_uuid);
                return exits.notFound({ message: 'Match not found' });
            }

            const results = { winner: inputs.winner };
            for (let i = 1; i <= 5; i++) { // Copy all 5 sets till the first missing set
                const setScore = inputs.results[`set${i}`];
                if (!setScore) break;
                results[`set${i}`] = setScore;
            }

            // Submit results to event_journal
            const response = await VolleyStationService.submitMatchResults({
                event, match, results, dateFinished: inputs.date_finished,
            });

            return exits.success(response);

        } catch (err) {
            loggers.errors_log.error('VolleyStation results submission error:', err);
            throw { message: 'Server Internal Error' };
        }
    }
};
