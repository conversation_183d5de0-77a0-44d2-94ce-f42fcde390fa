const path = require('path');
const fs = require('fs/promises');
const { chromium } = require('playwright-chromium');
const assetsPath = path.resolve(__dirname, '../../../../../assets/documents');

const STATIC_ASSETS_PATHS = {
    'refund_policy': path.join(assetsPath, 'refund_policy.pdf'),
    'cancellation_policy': path.join(assetsPath, 'refund_policy.pdf'),
};

const DEFAULT_PRODUCT_DESCRIPTION_TEXT = 'Booking or Reservation';

module.exports = {
    friendlyName: 'Get SW dispute evidence details',
    description: 'Returns dispute evidence details',

    inputs: {
        purchase: {
            type: 'string',
            required: true,
            description: 'The Stripe dispute id.',
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function ({ purchase: purchaseId }, exits) {

        try {
            const purchaseData = await __getPurchaseData(purchaseId)

            if (!purchaseData) {
                throw { validation: "Purchase doesn't exist" };
            }

            const data = await __getDefaultFields(purchaseData);
            const disputeEvidence = await StripeService.disputeEvidence.init(
                purchaseId,
                data
            );

            exits.success({
                disputeEvidence,
                purchase: {
                    email: purchaseData.customer_email_address,
                    name: purchaseData.customer_name,
                    ticket_history: purchaseData.ticket_history,
                    purchased_date: purchaseData.purchased_date,
                    scanned_at: purchaseData.scanned_at,
                }
            });
        } catch (err) {
            this.res.customRespError(err);
        }
    },
};

const __getDefaultFields = async (purchaseData) => {
    const { purchase_id, dispute_email_html } = purchaseData;
    const receiptHtml = await __getReceiptHtml(purchase_id);

    const defaultFileFields = await __getDefaultFileFields({ receiptHtml, dispute_email_html });
    const defaultTextFields = __getDefaultTextFields(purchaseData);

    return {
        ...defaultTextFields,
        ...defaultFileFields,
    };
};

const __getDefaultTextFields = (purchaseData) => {
    return {
        customer_name: purchaseData.customer_name,
        customer_email_address: purchaseData.customer_email_address,
        customer_purchase_ip: purchaseData.customer_purchase_ip,
        service_date: purchaseData.purchased_date,
        product_description: DEFAULT_PRODUCT_DESCRIPTION_TEXT,
        refund_policy_disclosure: 'REPLACE WITH REAL VALUE',
        refund_refusal_explanation: __getRefundRefusalExplanation(purchaseData.scanned_at),
        cancellation_policy_disclosure: 'REPLACE WITH REAL VALUE',
        access_activity_log: purchaseData.ticket_history || null
    };
}

const __getRefundRefusalExplanation = (scannedAt) => {
    if (!scannedAt) {
        return null
    }

    return `The ticket has been scanned and matched against with government ID and the user was admitted for the first time on ${scannedAt}.`
}

const __getPurchaseData = async (purchaseId) => {
    const query = `
        SELECT 
            p.purchase_id,
            (COALESCE(p.first, u.first) || ' ' || COALESCE(p.last, u.last)) customer_name,
            COALESCE(p.email, u.email) customer_email_address,
            TO_CHAR((p.created::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Dy, Mon DD, YYYY HH12:MI am') AS purchased_date,
            p.payer_ip customer_purchase_ip,
            p.payment_for,
            lp.tickets_scan ticket_history,
            TO_CHAR( 
                (lp.scanned_at::timestamptz AT TIME ZONE e.timezone), 
                'HH12:MI AM on Mon DD'
            ) scanned_at,
            (
                SELECT ee.email_html
                FROM event_email ee
                    INNER JOIN purchase lp 
                        ON (lp.purchase_id = p.purchase_id 
                        OR lp.linked_purchase_id = p.purchase_id) 
                        AND lp.is_ticket is TRUE
                    LEFT JOIN event_change ec
                    ON ee.event_email_id = ec.event_email_id
                WHERE ee.event_id = e.event_id
                    AND ec.purchase_id = lp.purchase_id
                    AND ee.reason_type = 'purchase dispute'
                ORDER BY ee.created DESC
                LIMIT 1
            ) AS dispute_email_html
        FROM purchase p
            LEFT JOIN purchase lp ON 
                (lp.purchase_id = p.purchase_id 
                OR lp.linked_purchase_id = p.purchase_id) 
                AND lp.is_ticket is TRUE
            INNER JOIN "user" u ON u.user_id = p.user_id
            INNER JOIN event e ON e.event_id = p.event_id
            WHERE p.purchase_id = $1
    `;
    return Db.query(query, [purchaseId]).then(({ rows }) => rows[0]);
};

const __getDefaultFileFields = async ({ receiptHtml = null, dispute_email_html = null }) => {
    const promises = Object.entries(STATIC_ASSETS_PATHS).map(async ([field, filePath]) => {
        const stripeFileObject = await __createStripeFileObject(field, filePath);
        return [field, stripeFileObject];
    })

    if (receiptHtml) {
        promises.push(__generateEvidenceFileFromHtml('receipt', receiptHtml));
    }

    if (dispute_email_html) {
        promises.push(__generateEvidenceFileFromHtml('customer_communication', dispute_email_html));
    }

    const stripeFiles = await Promise.all(promises);

    return Object.fromEntries(stripeFiles);
}

const __generateEvidenceFileFromHtml = async (fieldName, html) => {
    const filename = `${fieldName}.png`
    return [fieldName, await __generateStripeFileFromHtml(filename, html)]
}

const __createStripeFileObject = async (field, filePath) => {
    const fileExtname = path.extname(filePath);
    const filename = `${field}.${fileExtname}`
    const fileBuffer = await fs.readFile(filePath);

    return StripeService.files.uploadFile({
        file: fileBuffer,
        filename,
        purpose: StripeService.files.PURPOSE.DISPUTE_EVIDENCE
    });
}

const __generateStripeFileFromHtml = async (filename, html) => {
    const pdfBuffer = await __generateFileFromHtml(html);

    if (!pdfBuffer) {
        return null;
    }

    return StripeService.files.uploadFile({
        file: pdfBuffer,
        filename,
        purpose: StripeService.files.PURPOSE.DISPUTE_EVIDENCE
    });
}

const __generateFileFromHtml = async (html) => {
    const screenshotOptions = {
        type: 'png',
        fullPage: true,
        timeout: 30000,
    };

    try {
        const browser = await chromium.launch({
            args: ['--ignore-certificate-errors', '--no-sandbox'],
            env: { OPENSSL_CONF: '/dev/null' },
            headless: true,
        });

        const page = await browser.newPage();
        await page.setViewportSize({ width: 750, height: 1124 }); // або прибери, якщо хочеш авто
        await page.setContent(html, { waitUntil: 'networkidle', timeout: 30000 });

        const buffer = await page.screenshot(screenshotOptions);

        await browser.close();
        return buffer;
    } catch (err) {
        loggers.errors_log.error('Error generating image from HTML', err, html);
        return null;
    }
};

const __getReceiptHtml = async (purchaseId) => {
    const event_email = await __getPurchaseEmail(purchaseId);

    if (!event_email) {
        return null;
    }

    const email = await EmailService.getEmail(event_email.email_id);

    return email?.html || null;
}

const __getPurchaseEmail = async (purchaseId) => {
    const query = `
        SELECT *
        FROM event_email ee
             JOIN event_email_trigger eet ON (eet.event_id = ee.event_id OR eet.event_id = 0) AND
                                             eet.email_template_type = $1 AND
                                             eet.email_template_id = ee.email_template_id
        WHERE ee.purchase_id = $2
    `;

    return Db.query(
        query,
        [AEMService.TICKETS_PAYMENTS_GROUP_TYPE.ASSIGNED_TICKETS_RECEIPT, purchaseId]
    ).then(({ rows }) => rows[0]);
}
