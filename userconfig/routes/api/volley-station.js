module.exports = {

    /**
     * @api {get} /api/volley-station/v1/events/:eventId/schedule Get event schedule
     * @apiDescription Returns paginated schedule data for a specific event
     * @apiGroup VolleyStation API
     * @apiHeader {String} Authorization VolleyStation API key
     * @apiParam {Number} eventId Event ID
     * @apiParam {Number} [page=1] Page number for pagination
     * @apiParam {Number} [limit=50] Items per page (max 100)
     */
    'GET /api/volley-station/v1/events/:eventId/schedule': {
        action: 'v2/API/volley-station/schedule'
    },

    /**
     * @api {get} /api/volley-station/v1/events/:eventId/team-roster Get team roster data
     * @apiDescription Returns paginated team roster data including athletes and staff
     * @apiGroup VolleyStation API
     * @apiHeader {String} Authorization VolleyStation API key
     * @apiParam {Number} eventId Event ID
     * @apiParam {Number} [page=1] Page number for pagination
     * @apiParam {Number} [limit=50] Items per page (max 100)
     */
    'GET /api/volley-station/v1/events/:eventId/team-roster': {
        action: 'v2/API/volley-station/team-roster'
    },

    /**
     * @api {post} /api/volley-station/v1/events/:eventId/results Submit match results
     * @apiDescription Submit final match results to the event journal system
     * @apiGroup VolleyStation API
     * @apiHeader {String} Authorization VolleyStation API key
     * @apiParam {Number} eventId Event ID
     * @apiBody {String} match_uuid UUID of the match
     * @apiBody {Boolean} is_final Whether this is a final result submission (must be true)
     * @apiBody {String} date_finished ISO 8601 UTC timestamp when match finished
     * @apiBody {Number} [winner] Winning team (1 or 2)
     * @apiBody {Object} results Match results object with set scores
     * @apiBody {String} results.set1 First set score (required, format: "XX-YY")
     * @apiBody {String} [results.set2] Second set score (format: "XX-YY")
     * @apiBody {String} [results.set3] Third set score (format: "XX-YY")
     * @apiBody {String} [results.set4] Fourth set score (format: "XX-YY")
     * @apiBody {String} [results.set5] Fifth set score (format: "XX-YY")
     */
    'POST /api/volley-station/v1/events/:eventId/results': {
        action: 'v2/API/volley-station/results'
    }

};
