version: '3.8'
services:
    web:
        image: sw-main
        container_name: sw-main
        build:
            context: .
            dockerfile: Dockerfile
            args:
                UID: ${uid}
                GID: ${gid}
                ENV: ${NODE_ENV}
                LOG_PG_CS: ${LOG_PG_CS}
                LOG_APP_ID: ${LOG_APP_ID}
        ports:
            - ${HOST_PORT}:3000
        environment:
            - SW_DB=${SW_DB}
            - REDIS_URL=${REDIS_URL}
            - WORK_DIR=${WORK_DIR}
            - NODE_ENV=${NODE_ENV}
            - LOG_PG_CS=${LOG_PG_CS}
            - LOG_APP_ID=${LOG_APP_ID}
        volumes:
            - ${WORK_DIR}:/home/<USER>/app
            - ${WORK_DIR}/../.pm2/logs:/home/<USER>/.pm2/logs
            - ${WORK_DIR}/../logs:/home/<USER>/logs
            - ${WORK_DIR}/../connection:/home/<USER>/connection
            - ${WORK_DIR}/../sw-config:/home/<USER>/sw-config
            - ${WORK_DIR}/../passbook_keys:/home/<USER>/passbook_keys
        stop_grace_period: 1m
        restart: always
        init: true
        deploy:
            resources:
                limits:
                    memory: 1500M
networks:
    default:
        name: sw
        external: true
